import 'package:flutter/material.dart';
import '../models/download_task.dart';

class DownloadTaskCard extends StatelessWidget {
  final DownloadTask task;

  const DownloadTaskCard({
    super.key,
    required this.task,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE5E7EB)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.02),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title and Status
          Row(
            children: [
              Expanded(
                child: Text(
                  task.title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF111827),
                  ),
                ),
              ),
              _buildStatusBadge(),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // File info
          Row(
            children: [
              Text(
                'Size: ${task.size}',
                style: const TextStyle(
                  fontSize: 14,
                  color: Color(0xFF6B7280),
                ),
              ),
              if (task.status == DownloadStatus.downloading) ...[
                const SizedBox(width: 16),
                Text(
                  'Speed: ${task.speed}',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF6B7280),
                  ),
                ),
              ],
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Progress bar and percentage
          Row(
            children: [
              Expanded(
                child: Container(
                  height: 8,
                  decoration: BoxDecoration(
                    color: const Color(0xFFF3F4F6),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: FractionallySizedBox(
                    alignment: Alignment.centerLeft,
                    widthFactor: task.progress,
                    child: Container(
                      decoration: BoxDecoration(
                        color: _getProgressColor(),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Text(
                task.progressPercentage,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF111827),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusBadge() {
    Color backgroundColor;
    Color textColor;
    String text;

    switch (task.status) {
      case DownloadStatus.pending:
        backgroundColor = const Color(0xFFFEF3C7);
        textColor = const Color(0xFF92400E);
        text = 'Pending';
        break;
      case DownloadStatus.downloading:
        backgroundColor = const Color(0xFFDCFDF7);
        textColor = const Color(0xFF065F46);
        text = 'Downloading';
        break;
      case DownloadStatus.paused:
        backgroundColor = const Color(0xFFFEE2E2);
        textColor = const Color(0xFF991B1B);
        text = 'Paused';
        break;
      case DownloadStatus.completed:
        backgroundColor = const Color(0xFFD1FAE5);
        textColor = const Color(0xFF065F46);
        text = 'Completed';
        break;
      case DownloadStatus.canceled:
        backgroundColor = const Color(0xFFF3F4F6);
        textColor = const Color(0xFF374151);
        text = 'Canceled';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: textColor,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Color _getProgressColor() {
    switch (task.status) {
      case DownloadStatus.downloading:
        return const Color(0xFF3B82F6);
      case DownloadStatus.completed:
        return const Color(0xFF10B981);
      case DownloadStatus.paused:
        return const Color(0xFFF59E0B);
      case DownloadStatus.canceled:
        return const Color(0xFF6B7280);
      case DownloadStatus.pending:
        return const Color(0xFF8B5CF6);
    }
  }
}
