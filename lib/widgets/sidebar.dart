import 'package:flutter/material.dart';

class CustomSidebar extends StatefulWidget {
  final int selectedIndex;
  final Function(int) onItemSelected;

  const CustomSidebar({
    super.key,
    required this.selectedIndex,
    required this.onItemSelected,
  });

  @override
  State<CustomSidebar> createState() => _CustomSidebarState();
}

class _CustomSidebarState extends State<CustomSidebar> {

  final List<SidebarItem> sidebarItems = [
    SidebarItem(icon: Icons.home_outlined, selectedIcon: Icons.home),
    SidebarItem(icon: Icons.download_outlined, selectedIcon: Icons.download),
    SidebarItem(icon: Icons.search_outlined, selectedIcon: Icons.search),
    SidebarItem(icon: Icons.calendar_today_outlined, selectedIcon: Icons.calendar_today),
    SidebarItem(icon: Icons.people_outline, selectedIcon: Icons.people),
    SidebarItem(icon: Icons.settings_outlined, selectedIcon: Icons.settings),
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 80,
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          right: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Column(
        children: [
          const SizedBox(height: 20),
          
          // Logo/Brand area
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: const Color(0xFF6366F1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.play_arrow,
              color: Colors.white,
              size: 24,
            ),
          ),
          
          const SizedBox(height: 40),
          
          // Navigation Items
          ...sidebarItems.asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;
            final isSelected = widget.selectedIndex == index;

            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              child: GestureDetector(
                onTap: () {
                  widget.onItemSelected(index);
                },
                child: Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: isSelected ? const Color(0xFF6366F1).withOpacity(0.1) : Colors.transparent,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    isSelected ? item.selectedIcon : item.icon,
                    color: isSelected ? const Color(0xFF6366F1) : const Color(0xFF6B7280),
                    size: 24,
                  ),
                ),
              ),
            );
          }).toList(),
          
          const Spacer(),
          
          // Bottom spacing
          const SizedBox(height: 20),
        ],
      ),
    );
  }
}

class SidebarItem {
  final IconData icon;
  final IconData selectedIcon;

  SidebarItem({
    required this.icon,
    required this.selectedIcon,
  });
}
