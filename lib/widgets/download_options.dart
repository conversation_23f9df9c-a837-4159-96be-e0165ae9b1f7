import 'package:flutter/material.dart';

class DownloadOptionsPanel extends StatefulWidget {
  const DownloadOptionsPanel({super.key});

  @override
  State<DownloadOptionsPanel> createState() => _DownloadOptionsPanelState();
}

class _DownloadOptionsPanelState extends State<DownloadOptionsPanel> {
  bool season1Expanded = true;
  bool season2Expanded = true;
  
  Map<String, bool> selectedEpisodes = {};

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Download Options',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Color(0xFF111827),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 24),
        
        // Download Selected Button
        Row(
          children: [
            ElevatedButton.icon(
              onPressed: () {
                // Handle download selected
              },
              icon: const Icon(Icons.download, size: 18),
              label: const Text('Download Selected'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFF3F4F6),
                foregroundColor: const Color(0xFF6B7280),
                elevation: 0,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            
            const SizedBox(width: 12),
            
            TextButton(
              onPressed: () {
                // Handle select all
                setState(() {
                  selectedEpisodes.clear();
                  // Add all episodes to selection
                  for (var episode in season1Episodes) {
                    selectedEpisodes[episode.id] = true;
                  }
                  for (var episode in season2Episodes) {
                    selectedEpisodes[episode.id] = true;
                  }
                });
              },
              child: const Text(
                'Select All',
                style: TextStyle(
                  color: Color(0xFF6366F1),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 24),
        
        // Season 1
        _buildSeasonSection(
          'Season 1 (2016)',
          '4 episodes',
          season1Expanded,
          season1Episodes,
          () {
            setState(() {
              season1Expanded = !season1Expanded;
            });
          },
        ),
        
        const SizedBox(height: 16),
        
        // Season 2
        _buildSeasonSection(
          'Season 2 (2017)',
          '3 episodes',
          season2Expanded,
          season2Episodes,
          () {
            setState(() {
              season2Expanded = !season2Expanded;
            });
          },
        ),
      ],
    );
  }

  Widget _buildSeasonSection(
    String title,
    String episodeCount,
    bool isExpanded,
    List<Episode> episodes,
    VoidCallback onToggle,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE5E7EB)),
      ),
      child: Column(
        children: [
          // Season Header
          InkWell(
            onTap: onToggle,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Icon(
                    isExpanded ? Icons.keyboard_arrow_down : Icons.keyboard_arrow_right,
                    color: const Color(0xFF6B7280),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF111827),
                      ),
                    ),
                  ),
                  Text(
                    episodeCount,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF6B7280),
                    ),
                  ),
                  const SizedBox(width: 16),
                  TextButton(
                    onPressed: () {
                      setState(() {
                        bool allSelected = episodes.every((ep) => selectedEpisodes[ep.id] == true);
                        for (var episode in episodes) {
                          selectedEpisodes[episode.id] = !allSelected;
                        }
                      });
                    },
                    child: const Text(
                      'Select All',
                      style: TextStyle(
                        color: Color(0xFF6366F1),
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // Episodes List
          if (isExpanded) ...[
            const Divider(height: 1, color: Color(0xFFE5E7EB)),
            ...episodes.map((episode) => _buildEpisodeItem(episode)).toList(),
          ],
        ],
      ),
    );
  }

  Widget _buildEpisodeItem(Episode episode) {
    bool isSelected = selectedEpisodes[episode.id] ?? false;
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Color(0xFFF3F4F6), width: 1),
        ),
      ),
      child: Row(
        children: [
          Checkbox(
            value: isSelected,
            onChanged: (value) {
              setState(() {
                selectedEpisodes[episode.id] = value ?? false;
              });
            },
            activeColor: const Color(0xFF6366F1),
          ),
          
          const SizedBox(width: 12),
          
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  episode.title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF111827),
                  ),
                ),
                if (episode.subtitle != null) ...[
                  const SizedBox(height: 2),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: episode.isSpecial ? const Color(0xFF8B5CF6) : const Color(0xFF3B82F6),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      episode.subtitle!,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
          
          Text(
            episode.duration,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF6B7280),
            ),
          ),
        ],
      ),
    );
  }
}

class Episode {
  final String id;
  final String title;
  final String duration;
  final String? subtitle;
  final bool isSpecial;

  Episode({
    required this.id,
    required this.title,
    required this.duration,
    this.subtitle,
    this.isSpecial = false,
  });
}

final List<Episode> season1Episodes = [
  Episode(
    id: 's1e1',
    title: 'E01: Chapter One: The Vanishing of Will Byers',
    duration: '(47 min)',
  ),
  Episode(
    id: 's1e2',
    title: 'E02: Chapter Two: The Weirdo on Maple Street',
    duration: '(55 min)',
  ),
  Episode(
    id: 's1e3',
    title: 'E03: Chapter Three: Holly, Jolly',
    duration: '(51 min)',
  ),
  Episode(
    id: 's1trailer',
    title: 'Season 1 Trailer',
    subtitle: 'Trailer',
    duration: '(2 min)',
    isSpecial: false,
  ),
];

final List<Episode> season2Episodes = [
  Episode(
    id: 's2e1',
    title: 'E01: Chapter One: MADMAX',
    duration: '(48 min)',
  ),
  Episode(
    id: 's2e2',
    title: 'E02: Chapter Two: Trick or Treat, Freak',
    duration: '(56 min)',
  ),
  Episode(
    id: 's2special',
    title: 'Beyond Stranger Things',
    subtitle: 'Special',
    duration: '(22 min)',
    isSpecial: true,
  ),
];
