enum DownloadStatus {
  pending,
  downloading,
  paused,
  completed,
  canceled,
}

class DownloadTask {
  final String id;
  final String title;
  final String size;
  final String speed;
  final double progress; // 0.0 to 1.0
  final DownloadStatus status;

  DownloadTask({
    required this.id,
    required this.title,
    required this.size,
    required this.speed,
    required this.progress,
    required this.status,
  });

  String get progressPercentage => '${(progress * 100).round()}%';
}

// Sample data
final List<DownloadTask> sampleDownloadTasks = [
  DownloadTask(
    id: '1',
    title: 'Example TV Show S01E08',
    size: '1.5 GB',
    speed: '3.2 MB/s',
    progress: 0.45,
    status: DownloadStatus.downloading,
  ),
  DownloadTask(
    id: '2',
    title: 'Example TV Show S01E09',
    size: '1.5 GB',
    speed: '2.8 MB/s',
    progress: 0.78,
    status: DownloadStatus.downloading,
  ),
  DownloadTask(
    id: '3',
    title: 'Example Movie 2024',
    size: '2.1 GB',
    speed: '0 MB/s',
    progress: 0.32,
    status: DownloadStatus.paused,
  ),
  DownloadTask(
    id: '4',
    title: 'Example TV Show S01E01',
    size: '1.4 GB',
    speed: '0 MB/s',
    progress: 1.0,
    status: DownloadStatus.completed,
  ),
  DownloadTask(
    id: '5',
    title: 'Example TV Show S01E02',
    size: '1.4 GB',
    speed: '0 MB/s',
    progress: 1.0,
    status: DownloadStatus.completed,
  ),
  DownloadTask(
    id: '6',
    title: 'Example TV Show S01E03',
    size: '1.4 GB',
    speed: '0 MB/s',
    progress: 1.0,
    status: DownloadStatus.completed,
  ),
  DownloadTask(
    id: '7',
    title: 'Example TV Show S01E04',
    size: '1.4 GB',
    speed: '0 MB/s',
    progress: 1.0,
    status: DownloadStatus.completed,
  ),
  DownloadTask(
    id: '8',
    title: 'Example TV Show S01E05',
    size: '1.4 GB',
    speed: '0 MB/s',
    progress: 1.0,
    status: DownloadStatus.completed,
  ),
  DownloadTask(
    id: '9',
    title: 'Example Movie Failed',
    size: '2.3 GB',
    speed: '0 MB/s',
    progress: 0.15,
    status: DownloadStatus.canceled,
  ),
  DownloadTask(
    id: '10',
    title: 'Example TV Show S01E10',
    size: '1.5 GB',
    speed: '0 MB/s',
    progress: 0.0,
    status: DownloadStatus.pending,
  ),
  DownloadTask(
    id: '11',
    title: 'Example TV Show S01E11',
    size: '1.5 GB',
    speed: '0 MB/s',
    progress: 0.0,
    status: DownloadStatus.pending,
  ),
];
